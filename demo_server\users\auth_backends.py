"""
Custom Keycloak OIDC Authentication Backend for Django Face Recognition System
"""

from mozilla_django_oidc.auth import OIDCAuthenticationBackend
from django.contrib.auth.models import User
from django.conf import settings
from django.utils import timezone
import requests
import logging

from .models import UserProfile

logger = logging.getLogger(__name__)


class KeycloakOIDCAuthenticationBackend(OIDCAuthenticationBackend):
    """
    Custom OIDC Authentication Backend for Keycloak integration
    Handles user creation, role mapping, and permission management
    """

    def create_user(self, claims):
        """
        Create a new user based on OIDC claims
        """
        user = super().create_user(claims)
        
        # Get user roles from Keycloak
        roles = self.get_user_roles(claims)
        
        # Create or update user profile
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={
                'preferred_recognizer': 'facenet',
                'is_organizational_admin': False
            }
        )
        
        # Update profile from Keycloak data
        profile.update_from_keycloak(claims, roles)
        
        logger.info(f"Created new user {user.username} with roles: {roles}")
        return user

    def update_user(self, user, claims):
        """
        Update existing user based on OIDC claims
        """
        # Get user roles from Keycloak
        roles = self.get_user_roles(claims)
        
        # Get or create user profile
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={
                'preferred_recognizer': 'facenet',
                'is_organizational_admin': False
            }
        )
        
        # Update profile from Keycloak data
        profile.update_from_keycloak(claims, roles)
        
        logger.info(f"Updated user {user.username} with roles: {roles}")
        return user

    def get_user_roles(self, claims):
        """
        Extract user realm and client roles from OIDC claims or fetch from Keycloak
        Returns dict with 'realm_roles' and 'client_roles'
        """
        print("===================================")
        print(f"Claims received: {claims}")

        for key, value in claims.items():
            print(f"  {key}: {value}")
        print("===================================")

        realm_roles = []
        client_roles = []
        
        # Check for realm roles
        if 'realm_access' in claims and 'roles' in claims['realm_access']:
            realm_roles = [role for role in claims['realm_access']['roles'] 
                          if role in settings.KEYCLOAK_REALM_ROLES]
        
        # Check for client roles
        if 'resource_access' in claims:
            client_id = settings.KEYCLOAK_CLIENT_ID
            if client_id in claims['resource_access'] and 'roles' in claims['resource_access'][client_id]:
                client_roles = [role for role in claims['resource_access'][client_id]['roles']
                               if role in settings.KEYCLOAK_CLIENT_ROLES]
        
        # If no roles found in claims, fetch from Keycloak API
        if (not realm_roles and not client_roles) and 'sub' in claims:
            api_roles = self.fetch_user_roles_from_keycloak(claims['sub'])
            realm_roles = api_roles.get('realm_roles', [])
            client_roles = api_roles.get('client_roles', [])
        
        return {
            'realm_roles': realm_roles,
            'client_roles': client_roles
        }

    def fetch_user_roles_from_keycloak(self, user_id):
        """
        Fetch user realm and client roles directly from Keycloak Admin API
        Returns dict with 'realm_roles' and 'client_roles'
        """
        try:
            # Get admin token
            admin_token = self.get_admin_token()
            if not admin_token:
                logger.error("Could not obtain admin token from Keycloak")
                return {'realm_roles': [], 'client_roles': []}
            
            headers = {
                'Authorization': f'Bearer {admin_token}',
                'Content-Type': 'application/json'
            }
            
            # Fetch realm roles
            realm_roles_url = f"{settings.KEYCLOAK_SERVER_URL}/admin/realms/{settings.KEYCLOAK_REALM}/users/{user_id}/role-mappings/realm"
            realm_response = requests.get(realm_roles_url, headers=headers)
            
            realm_roles = []
            if realm_response.status_code == 200:
                roles_data = realm_response.json()
                realm_roles = [role['name'] for role in roles_data 
                              if role['name'] in settings.KEYCLOAK_REALM_ROLES]
            
            # Fetch client roles
            client_roles = []
            # First get the client internal ID
            clients_url = f"{settings.KEYCLOAK_SERVER_URL}/admin/realms/{settings.KEYCLOAK_REALM}/clients"
            clients_response = requests.get(clients_url, headers=headers, params={'clientId': settings.KEYCLOAK_CLIENT_ID})
            
            if clients_response.status_code == 200:
                clients_data = clients_response.json()
                if clients_data:
                    client_uuid = clients_data[0]['id']
                    
                    # Get client roles for user
                    client_roles_url = f"{settings.KEYCLOAK_SERVER_URL}/admin/realms/{settings.KEYCLOAK_REALM}/users/{user_id}/role-mappings/clients/{client_uuid}"
                    client_response = requests.get(client_roles_url, headers=headers)
                    
                    if client_response.status_code == 200:
                        client_roles_data = client_response.json()
                        client_roles = [role['name'] for role in client_roles_data 
                                       if role['name'] in settings.KEYCLOAK_CLIENT_ROLES]
            
            return {
                'realm_roles': realm_roles,
                'client_roles': client_roles
            }
                
        except Exception as e:
            logger.error(f"Error fetching user roles from Keycloak: {str(e)}")
        
        return {'realm_roles': [], 'client_roles': []}

    def get_admin_token(self):
        """
        Get an admin token from Keycloak for API calls
        """
        try:
            token_url = f"{settings.KEYCLOAK_SERVER_URL}/realms/{settings.KEYCLOAK_REALM}/protocol/openid-connect/token"
            
            data = {
                'grant_type': 'client_credentials',
                'client_id': settings.KEYCLOAK_CLIENT_ID,
                'client_secret': settings.KEYCLOAK_CLIENT_SECRET
            }
            
            response = requests.post(token_url, data=data)
            
            if response.status_code == 200:
                token_data = response.json()
                return token_data.get('access_token')
            else:
                logger.error(f"Failed to get admin token: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"Error getting admin token: {str(e)}")
        
        return None

    def get_username(self, claims):
        """
        Generate username from OIDC claims
        """
        # Try different claim fields for username
        username = claims.get('preferred_username')
        if not username:
            username = claims.get('email')
        if not username:
            username = claims.get('sub')  # Use sub as fallback
        
        return username

    def filter_users_by_claims(self, claims):
        """
        Filter users based on OIDC claims
        """
        username = self.get_username(claims)
        if not username:
            return self.UserModel.objects.none()
        
        # Try to find user by username first
        try:
            user = self.UserModel.objects.get(username=username)
            return self.UserModel.objects.filter(pk=user.pk)
        except self.UserModel.DoesNotExist:
            pass
        
        # Try to find user by Keycloak ID
        if 'sub' in claims:
            try:
                profile = UserProfile.objects.get(keycloak_id=claims['sub'])
                return self.UserModel.objects.filter(pk=profile.user.pk)
            except UserProfile.DoesNotExist:
                pass
        
        # Try to find user by email
        if 'email' in claims:
            try:
                user = self.UserModel.objects.get(email=claims['email'])
                return self.UserModel.objects.filter(pk=user.pk)
            except self.UserModel.DoesNotExist:
                pass
        
        return self.UserModel.objects.none()

    def describe_user_by_claims(self, claims):
        """
        Return a description of the user based on claims
        """
        return f"User {self.get_username(claims)} from Keycloak"