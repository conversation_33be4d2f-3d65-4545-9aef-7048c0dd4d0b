from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from recognition.facenet_recognizer import FaceNetRecognizer
from .models import AlertPhoto, <PERSON>ert<PERSON>erson, Alarm, Venue
from .forms import AlertPhotoForm, AlertPersonForm, RenameUnknownPersonForm
from .utils import calculate_image_vector, detect_faces
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from .serializers import AlertPersonSerializer, AlertPhotoSerializer, AlarmSerializer
import numpy as np
from PIL import Image
from django.core.files.base import ContentFile
from io import BytesIO
from django.contrib import messages
from rest_framework.permissions import IsAuthenticated
from django.db.models import Count
import os
import shutil
from django.conf import settings
import logging
from datetime import datetime, date


logger = logging.getLogger(__name__)

@login_required
def index(request):
    total_persons = AlertPerson.objects.filter(user=request.user).count()
    return render(request, 'alerts/index.html', {'total_persons': total_persons})

@login_required
def delete_multiple_alerts(request):
    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'delete_all':
            # Tüm alarmları sil
            Alarm.objects.filter(person__user=request.user).delete()
            messages.success(request, 'All alerts deleted successfully.')

        elif action == 'delete_selected':
            selected_alerts = request.POST.getlist('selected_alerts')
            if selected_alerts:
                # Seçili alarmları sil
                Alarm.objects.filter(
                    id__in=selected_alerts,
                    person__user=request.user
                ).delete()
                messages.success(request, 'Selected alerts deleted successfully.')
            else:
                messages.warning(request, 'No alerts were selected.')

    return redirect('alerts:alert_list')

@login_required
def add_alert_photo(request):
    if request.method == 'POST':
        form = AlertPhotoForm(request.POST, request.FILES)
        if form.is_valid():
            person_id = form.cleaned_data['person']
            person = get_object_or_404(AlertPerson, id=person_id, user=request.user)
            photos = request.FILES.getlist('photos')
            for photo in photos:
                image = Image.open(photo)
                face_boxes = detect_faces(np.array(image))
                if not face_boxes:
                    messages.error(request, f'No face detected in {photo.name}')
                else:
                    face_box = face_boxes[0]
                    face_image = image.crop(face_box)
                    if face_image.size[0] < 100 or face_image.size[1] < 100:
                        messages.error(request, f'Face is too small in {photo.name}')
                    else:
                        alert_photo = AlertPhoto(person=person)
                        buffer = BytesIO()
                        face_image.save(buffer, format='JPEG')
                        alert_photo.photo.save(f"{person.name}_face.jpg", ContentFile(buffer.getvalue()))

                        # Önce fotoğrafı kaydet
                        alert_photo.save()
                        messages.success(request, f'Photo {photo.name} added successfully')

            return redirect('alerts:person_detail', person_id=person.id)
    else:
        form = AlertPhotoForm()
    return render(request, 'alerts/add_photo.html', {'form': form})

@login_required
def add_alert_person(request):
    if request.method == 'POST':
        form = AlertPersonForm(request.POST)
        if form.is_valid():
            alert_person = form.save(commit=False)
            alert_person.user = request.user  # Set the user to the currently logged-in user
            alert_person.save()
            messages.success(request, 'Alert person added successfully.')
            return redirect('alerts:index')  # Redirect to the index page
    else:
        form = AlertPersonForm()
    return render(request, 'alerts/add_person.html', {'form': form})


@login_required
def person_detail(request, person_id):
    person = get_object_or_404(AlertPerson, id=person_id, user=request.user)
    
    # Her iki modeli de başlat
    facenet_recognizer = FaceNetRecognizer()

    if request.method == 'POST':
        form = AlertPhotoForm(request.POST, request.FILES)
        if form.is_valid():
            photos = request.FILES.getlist('photos')
            for photo in photos:
                image = Image.open(photo)
                face_boxes, faces = detect_faces(np.array(image))

                if len(face_boxes) == 0:
                    messages.error(request, f'No face detected in {photo.name}')
                else:
                    face_boxes = face_boxes[:, :-1]
                    face_boxes = face_boxes.astype(int)
                    face_boxes = face_boxes + [-1, -1, 1, 1]
                    face_box = face_boxes[0]
                    face_width = face_box[2] - face_box[0]
                    face_height = face_box[3] - face_box[1]
                    if face_width < 100 or face_height < 100:
                        messages.error(request, f'Face is too small in {photo.name}')
                        form = AlertPhotoForm()
                        return render(request, 'alerts/add_photo.html', {'person': person, 'form': form})
                    else:
                        alert_photo = AlertPhoto(person=person)
                        buffer = BytesIO()
                        image.save(buffer, format='JPEG')
                        alert_photo.photo.save(f"{person.name}_face.jpg", ContentFile(buffer.getvalue()))

                        # Önce fotoğrafı kaydet ki path'i olsun
                        alert_photo.save()

                        # Her iki modelin facebankına ekle
                        facenet_success = facenet_recognizer.add_face(person.name, alert_photo.photo.path)
                        
                        # Her iki model için embeddingler oluştur ve kaydet
                        try:
                            # FaceNet embeddingi hesapla
                            from alerts.utils import calculate_image_vector
                            facenet_vector = calculate_image_vector(alert_photo.photo.path, 'facenet')
                            if facenet_vector is not None:
                                alert_photo.image_vector_facenet = np.array(facenet_vector, dtype=np.float32).tobytes()
                                
                            # Güncellenmiş embedding'lerle kaydet
                            alert_photo.save()
                        except Exception as e:
                            logger.error(f"Error generating face embeddings: {str(e)}")

                        if facenet_success:
                            messages.success(request, f'Photo {photo.name} added successfully and added to facebank')
                        else:
                            messages.warning(request, f'Photo added to FaceNet facebank only: {photo.name}')

            return redirect('alerts:person_detail', person_id=person.id)
    else:
        form = AlertPhotoForm()

    return render(request, 'alerts/add_photo.html', {'person': person, 'form': form})

@login_required
def person_list(request):
    persons = AlertPerson.objects.filter(user=request.user).annotate(
        photo_count=Count('photos'),
        alarm_count=Count('alarm')
    )
    return render(request, 'alerts/person_list.html', {'persons': persons})

@login_required
def person_times(request, person_id):
    person = get_object_or_404(AlertPerson, id=person_id, user=request.user)

    # Tarih parametresini al, yoksa bugünü kullan
    selected_date_str = request.GET.get('date')
    if selected_date_str:
        try:
            selected_date = datetime.strptime(selected_date_str, '%Y-%m-%d').date()
        except ValueError:
            selected_date = date.today()
    else:
        selected_date = date.today()

    # Seçilen tarihteki alarmları al (sadece ENTRY/EXIT)
    alarms = Alarm.objects.filter(
        person=person,
        date__date=selected_date,
        alarm_type__in=['ENTRY', 'EXIT']
    ).select_related('camera', 'camera__venue').order_by('camera__venue', 'date')

    # VENUE bazında verileri grupla
    venue_summary = {}
    has_data = False

    for alarm in alarms:
        venue_name = alarm.camera.venue.name if alarm.camera.venue else "Venue Atanmamış"
        venue_id = alarm.camera.venue.id if alarm.camera.venue else 'unassigned'
        
        if venue_id not in venue_summary:
            venue_summary[venue_id] = {
                'venue_name': venue_name,
                'venue_obj': alarm.camera.venue,
                'entry_exit_pairs': [],
                'total_events': 0,
                'all_alarms': [],
                'cameras_used': set()
            }

        venue_summary[venue_id]['all_alarms'].append(alarm)
        venue_summary[venue_id]['total_events'] += 1
        venue_summary[venue_id]['cameras_used'].add(alarm.camera)
        has_data = True

    # Her venue için alarmları giriş-çıkış çiftlerine dönüştür
    for venue_id, data in venue_summary.items():
        all_alarms = data['all_alarms']
        pairs = []

        # Alarmları entry-exit mantığına göre çiftleştir
        # Entry ile başlayıp Exit ile devam eden çiftleri bul
        current_entry = None
        
        for alarm in all_alarms:
            if alarm.alarm_type == 'ENTRY':
                # Önceki entry varsa onu yalnız bırak
                if current_entry:
                    pairs.append({
                        'entry': current_entry,
                        'exit': None,
                        'entry_camera': current_entry.camera,
                        'exit_camera': None,
                        'duration': None,
                        'hours': None,
                        'minutes': None,
                    })
                current_entry = alarm
                
            elif alarm.alarm_type == 'EXIT' and current_entry:
                # Entry-Exit çifti buldu
                duration = alarm.date - current_entry.date
                total_seconds = int(duration.total_seconds())
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                
                pairs.append({
                    'entry': current_entry,
                    'exit': alarm,
                    'entry_camera': current_entry.camera,
                    'exit_camera': alarm.camera,
                    'duration': duration,
                    'hours': hours,
                    'minutes': minutes,
                })
                current_entry = None

        # Son entry varsa onu da ekle
        if current_entry:
            pairs.append({
                'entry': current_entry,
                'exit': None,
                'entry_camera': current_entry.camera,
                'exit_camera': None,
                'duration': None,
                'hours': None,
                'minutes': None,
            })

        data['entry_exit_pairs'] = pairs

    context = {
        'person': person,
        'selected_date': selected_date,
        'venue_summary': venue_summary,
        'has_data': has_data,
    }

    return render(request, 'alerts/person_times.html', context)

class AlertPersonViewSet(viewsets.ModelViewSet):
    serializer_class = AlertPersonSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['name']
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Return objects for the currently authenticated user only
        return AlertPerson.objects.filter(user=self.request.user)

    @action(detail=True, methods=['post'])
    def add_photo(self, request, pk=None):
        person = self.get_object()
        serializer = AlertPhotoSerializer(data=request.data)
        if serializer.is_valid():
            photo = serializer.save(person=person)
            image_vector = calculate_image_vector(photo.photo.path)
            photo.image_vector = image_vector
            photo.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class AlarmViewSet(viewsets.ModelViewSet):
    queryset = Alarm.objects.all()
    serializer_class = AlarmSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['person__name', 'camera__name', 'date']

    @action(detail=False, methods=['post'])
    def cleanup(self, request):
        # Implement synchronous cleanup logic here
        return Response(status=status.HTTP_202_ACCEPTED)

@login_required
def alert_list(request):
    alerts = Alarm.objects.filter(person__user=request.user).select_related(
        'person', 'camera', 'alert_photo'
    ).order_by('-date')
    paginator = Paginator(alerts, 10)  # 10 alerts per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    return render(request, 'alerts/alert_list.html', {'page_obj': page_obj})

@login_required
def unknown_persons_list(request):
    """View to list all unknown persons"""
    unknown_persons = AlertPerson.objects.filter(
        user=request.user,
        is_unknown=True
    ).order_by('-last_seen_date')

    paginator = Paginator(unknown_persons, 10)  # Show 10 unknown persons per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'alerts/unknown_persons_list.html', {
        'page_obj': page_obj,
    })

@login_required
def unknown_person_detail(request, person_id):
    """View to show details of an unknown person and their photos"""
    person = get_object_or_404(AlertPerson, id=person_id, user=request.user, is_unknown=True)
    photos = AlertPhoto.objects.filter(person=person).order_by('-created_at')
    
    # Initialize the form for renaming
    form = RenameUnknownPersonForm()
    
    paginator = Paginator(photos, 5)  # Show 5 photos per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    return render(request, 'alerts/unknown_person_detail.html', {
        'person': person,
        'page_obj': page_obj,
        'form': form
    })

@login_required
def rename_unknown_person(request, person_id):
    """View to rename an unknown person"""
    person = get_object_or_404(AlertPerson, id=person_id, user=request.user, is_unknown=True)
    
    if request.method == 'POST':
        form = RenameUnknownPersonForm(request.POST)
        if form.is_valid():
            new_name = form.cleaned_data['new_name']
            old_name = person.name
            
            # Update the filesystem folders
            success = rename_person_folder(old_name, new_name)
            
            if success:
                # Önce yeni kişiyi oluştur
                new_person = AlertPerson.objects.create(
                    user=person.user,
                    name=new_name,
                    is_unknown=False,
                    first_seen_camera=person.first_seen_camera,
                    last_seen_date=person.last_seen_date,
                    created_at=person.created_at,
                    updated_at=person.updated_at
                )
                
                # Fotoğrafları yeni kişiye taşı
                AlertPhoto.objects.filter(person=person).update(person=new_person)
                
                # Alarmları yeni kişiye taşı
                Alarm.objects.filter(person=person).update(person=new_person)
                
                # Fotoğraf yollarını güncelle
                update_photo_paths(new_person, old_name, new_name)
                
                # Eski Unknown kişiyi sil
                person.delete()
                
                # Update only the names in facebank instead of rebuilding embeddings
                update_facebank_names(old_name, new_name)
                
                messages.success(request, f'Person renamed from {old_name} to {new_name} successfully.')
                return redirect('alerts:person_list')
            else:
                messages.error(request, 'Failed to rename the folder. Please try again.')
    else:
        form = RenameUnknownPersonForm()
    
    photos = AlertPhoto.objects.filter(person=person).order_by('-created_at')
    paginator = Paginator(photos, 5)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    return render(request, 'alerts/unknown_person_detail.html', {
        'person': person,
        'page_obj': page_obj,
        'form': form
    })

def rename_person_folder(old_name, new_name):
    """Rename person folder and move from unknowns to regular folder"""
    try:
        # Paths
        base_dir = settings.MEDIA_ROOT
        unknown_dir = os.path.join(base_dir, 'alert_photos', 'unknowns')
        known_dir = os.path.join(base_dir, 'alert_photos')
        
        # Source and destination paths
        src_path = os.path.join(unknown_dir, old_name)
        dst_path = os.path.join(known_dir, new_name)
        
        # Check if source directory exists
        if not os.path.exists(src_path):
            logger.error(f"Source directory {src_path} does not exist")
            return False
        
        # Ensure destination directory doesn't exist
        if os.path.exists(dst_path):
            logger.error(f"Destination directory {dst_path} already exists")
            return False
        
        # Create destination directory
        os.makedirs(dst_path, exist_ok=True)
        
        # Move all files from source to destination
        for filename in os.listdir(src_path):
            src_file = os.path.join(src_path, filename)
            dst_file = os.path.join(dst_path, filename)
            shutil.copy2(src_file, dst_file)
        
        # Remove the source directory after successful copy
        shutil.rmtree(src_path)
        
        return True
    except Exception as e:
        logger.error(f"Error in rename_person_folder: {str(e)}")
        return False

def update_facebank_names(old_name, new_name):
    """Update only the names in facebank without changing embeddings"""
    try:
        # Update FaceNet recognizer
        facenet = FaceNetRecognizer()
        success = facenet.rename_person_in_facebank(old_name, new_name)
        
        if success:
            logger.info(f"Successfully renamed {old_name} to {new_name} in facebank")
        else:
            logger.error(f"Failed to rename {old_name} to {new_name} in facebank")
        
        return success
    except Exception as e:
        logger.error(f"Error updating facebank names: {str(e)}")
        return False

def update_person_embeddings(person_name):
    """Update only the embeddings for a specific person instead of rebuilding the entire facebank"""
    try:
        # Update FaceNet recognizer
        facenet = FaceNetRecognizer()
        facenet.update_person_embeddings(person_name)
        
        return True
    except Exception as e:
        logger.error(f"Error updating person embeddings: {str(e)}")
        return False


def update_photo_paths(person, old_name, new_name):
    """Update photo paths in database after renaming a person"""
    try:
        photos = AlertPhoto.objects.filter(person=person)
        for photo in photos:
            if photo.photo:
                # Eski dosya yolu: media/alert_photos/unknowns/old_name/filename.jpg
                # Yeni dosya yolu: media/alert_photos/new_name/filename.jpg
                old_path = photo.photo.name
                
                # Dosya adını al (ör: example_face.jpg)
                filename = os.path.basename(old_path)
                
                # Yeni yolu oluştur
                new_path = os.path.join('alert_photos', new_name, filename)
                
                # Veritabanındaki photo alanını güncelle
                photo.photo.name = new_path
                photo.save()
                
        return True
    except Exception as e:
        logger.error(f"Error updating photo paths: {str(e)}")
        return False

@login_required
def delete_photo(request, photo_id):
    photo = get_object_or_404(AlertPhoto, id=photo_id)
    
    # Kullanıcının bu fotoğrafa erişim yetkisini kontrol et
    if photo.person.user != request.user:
        messages.error(request, 'You do not have permission to delete this photo.')
        return redirect('alerts:person_detail', person_id=photo.person.id)
    
    person_id = photo.person.id
    person_name = photo.person.name
    
    if request.method == 'POST':
        # Fotoğraf dosyasını silmek için
        if photo.photo:
            if os.path.isfile(photo.photo.path):
                os.remove(photo.photo.path)
                
        # Fotoğrafı veritabanından sil
        photo.delete()
        messages.success(request, 'Photo deleted successfully.')
        
        # Bu kişinin diğer fotoğrafları var mı kontrol et
        remaining_photos = AlertPhoto.objects.filter(person_id=person_id).exists()
        
        # Eğer primary foto sildiysen ve başka foto varsa, birini primary yap
        if not AlertPhoto.objects.filter(person_id=person_id, is_primary=True).exists() and remaining_photos:
            first_photo = AlertPhoto.objects.filter(person_id=person_id).first()
            if first_photo:
                first_photo.is_primary = True
                first_photo.save()
        
        # FaceNet model için bu kişinin embeddings verilerini güncelle
        try:
            # FaceNet için
            from recognition.facenet_recognizer import FaceNetRecognizer
            facenet_recognizer = FaceNetRecognizer()
            
            # Kişi için embeddings'leri güncelle
            facenet_recognizer.update_person_embeddings(person_name)
            
            logger.info(f"Updated embeddings for person {person_name} after photo deletion")
        except Exception as e:
            logger.error(f"Error updating embeddings for person {person_name}: {str(e)}")
        
        return redirect('alerts:person_detail', person_id=person_id)
    
    # GET isteği için onay sayfasını göster
    return render(request, 'alerts/confirm_photo_delete.html', {'photo': photo})

@login_required
def delete_selected_persons(request):
    if request.method == 'POST':
        selected_persons = request.POST.getlist('selected_persons')
        if selected_persons:
            from recognition.facenet_recognizer import FaceNetRecognizer
            
            # FaceNet modelini başlat
            facenet_recognizer = FaceNetRecognizer()
            
            deleted_persons = []
            
            for person_id in selected_persons:
                try:
                    # Kişiyi veritabanından al
                    person = get_object_or_404(AlertPerson, id=person_id, user=request.user)
                    person_name = person.name
                    
                    # Kişiye ait fotoğraf sayısını kontrol et
                    photos = AlertPhoto.objects.filter(person=person)
                    has_photos = photos.exists()
                    
                    # Kişiye ait tüm fotoğrafları sil (varsa)
                    if has_photos:
                        for photo in photos:
                            if photo.photo and os.path.isfile(photo.photo.path):
                                os.remove(photo.photo.path)
                    
                    # Kişiye ait model dosyalarını sil - Kişinin kendi klasörü (sadece fotoğrafı varsa)
                    if has_photos:
                        facebank_path = settings.RECOGNITION_CONFIG['facebank_path']
                        
                        # Kişinin kendi klasörünü sil (fotoğraflar)
                        person_dir = os.path.join(facebank_path, person_name)
                        if os.path.exists(person_dir):
                            logger.info(f"Deleting directory for person {person_name}: {person_dir}")
                            shutil.rmtree(person_dir)
                        
                        # Bilinmeyen kişiyse, unknowns klasöründe de kontrol et
                        if person.is_unknown:
                            unknown_dir = os.path.join(facebank_path, 'unknowns', person_name)
                            if os.path.exists(unknown_dir):
                                logger.info(f"Deleting unknown directory for person {person_name}: {unknown_dir}")
                                shutil.rmtree(unknown_dir)
                            
                            # Unknown facebank'tan da sil
                            try:
                                unknown_removed = facenet_recognizer.unknown_manager.remove_unknown_person(person_name)
                                if unknown_removed:
                                    logger.info(f"Successfully removed {person_name} from unknown facebank")
                                else:
                                    logger.warning(f"Failed to remove {person_name} from unknown facebank")
                            except Exception as e:
                                logger.error(f"Error removing {person_name} from unknown facebank: {str(e)}")
                        
                        # Model dosyalarından kaldır - Bu işlemi kişiyi silmeden önce yap (sadece fotoğrafı varsa)
                        logger.info(f"Updating model embeddings for person {person_name}")
                        facenet_success = facenet_recognizer.update_person_embeddings(person_name)
                        
                        if not facenet_success:
                            logger.warning(f"Failed to update FaceNet embeddings for {person_name}")
                    else:
                        # Fotoğrafı olmayan kişi için log
                        logger.info(f"Person {person_name} has no photos, skipping model cleanup")
                    
                    # Kişiyi veritabanından sil (alarmlar cascade ile silinecek)
                    person.delete()
                    
                    # Model dosyalarının temizlendiğini doğrula (sadece fotoğrafı varsa)
                    if has_photos:
                        cleanup_verified = verify_and_cleanup_model_files(person_name, logger)
                        if cleanup_verified:
                            deleted_persons.append(person_name)
                        else:
                            logger.warning(f"Model cleanup may be incomplete for person: {person_name}")
                            deleted_persons.append(f"{person_name} (model cleanup warning)")
                    else:
                        # Fotoğrafı olmayan kişi başarıyla silindi
                        deleted_persons.append(person_name)
                        logger.info(f"Successfully deleted person without photos: {person_name}")
                    
                except Exception as e:
                    logger.error(f"Error deleting person {person_id}: {str(e)}")
                    messages.error(request, f"Error deleting person: {str(e)}")
            
            # Başarılı mesajı
            if deleted_persons:
                logger.info(f"Successfully deleted persons: {', '.join(deleted_persons)}")
                messages.success(request, f"{len(deleted_persons)} person(s) and related data deleted successfully.")
        else:
            messages.warning(request, "No persons were selected.")
    
    return redirect('alerts:person_list')

def verify_and_cleanup_model_files(person_name, logger=None):
    """
    Helper function to verify and clean up model files for a deleted person
    Returns True if cleanup was successful, False otherwise
    """
    if not logger:
        import logging
        logger = logging.getLogger(__name__)
    
    try:
        from recognition.facenet_recognizer import FaceNetRecognizer
        
        # Initialize recognizer
        facenet_recognizer = FaceNetRecognizer()
        
        # Check if person still exists in model files and remove if necessary
        facenet_cleaned = False
            
        # Check FaceNet model files  
        if facenet_recognizer.names is not None and person_name in facenet_recognizer.names:
            logger.warning(f"Person {person_name} still found in FaceNet model, attempting cleanup")
            facenet_cleaned = facenet_recognizer.update_person_embeddings(person_name)
        else:
            facenet_cleaned = True
        
        if facenet_cleaned:
            logger.info(f"Model cleanup verified successful for person: {person_name}")
            return True
        else:
            logger.error(f"Model cleanup failed for person: {person_name}")
            return False
            
    except Exception as e:
        logger.error(f"Error during model cleanup verification for {person_name}: {str(e)}")
        return False


# ============================
# VENUE MANAGEMENT VIEWS
# ============================

@login_required
def venue_list(request):
    """Venue listesi - kullanıcının venue'larını listele"""
    venues = Venue.objects.filter(user=request.user).order_by('name')
    
    # Seçili venue'ları silme işlemi
    if request.method == 'POST':
        venue_ids = request.POST.getlist('venue_ids')
        if venue_ids:
            deleted_count = Venue.objects.filter(id__in=venue_ids, user=request.user).count()
            Venue.objects.filter(id__in=venue_ids, user=request.user).delete()
            messages.success(request, f'{deleted_count} venue başarıyla silindi.')
        return redirect('alerts:venue_list')
    
    context = {
        'venues': venues,
    }
    return render(request, 'alerts/venue_list.html', context)


@login_required
def venue_create(request):
    """Yeni venue oluştur"""
    if request.method == 'POST':
        name = request.POST.get('name', '').strip()
        description = request.POST.get('description', '').strip()
        is_active = request.POST.get('is_active') == 'on'
        
        if name:
            venue = Venue.objects.create(
                user=request.user,
                name=name,
                description=description,
                is_active=is_active
            )
            messages.success(request, f'Venue "{venue.name}" başarıyla oluşturuldu.')
            return redirect('alerts:venue_list')
        else:
            messages.error(request, 'Venue adı zorunludur.')
    
    return render(request, 'alerts/venue_create.html')


@login_required
def venue_edit(request, venue_id):
    """Venue düzenle"""
    venue = get_object_or_404(Venue, id=venue_id, user=request.user)
    
    if request.method == 'POST':
        name = request.POST.get('name', '').strip()
        description = request.POST.get('description', '').strip()
        is_active = request.POST.get('is_active') == 'on'
        
        if name:
            venue.name = name
            venue.description = description
            venue.is_active = is_active
            venue.save()
            messages.success(request, f'Venue "{venue.name}" başarıyla güncellendi.')
            return redirect('alerts:venue_list')
        else:
            messages.error(request, 'Venue adı zorunludur.')
    
    context = {
        'venue': venue,
        'camera_count': venue.cameras.count(),
    }
    return render(request, 'alerts/venue_edit.html', context)


@login_required
def venue_delete(request, venue_id):
    """Venue sil"""
    venue = get_object_or_404(Venue, id=venue_id, user=request.user)
    
    if request.method == 'POST':
        venue_name = venue.name
        venue.delete()
        messages.success(request, f'Venue "{venue_name}" başarıyla silindi.')
        return redirect('alerts:venue_list')
    
    context = {
        'venue': venue,
        'camera_count': venue.cameras.count(),
    }
    return render(request, 'alerts/venue_delete.html', context)