"""
Custom middleware for Keycloak role-based access control
"""

from django.http import HttpResponseForbidden
from django.shortcuts import redirect
from django.urls import reverse
from django.contrib import messages
from django.utils.deprecation import MiddlewareMixin
import logging

logger = logging.getLogger(__name__)


class KeycloakRoleMiddleware(MiddlewareMixin):
    """
    Middleware to enforce role-based access control based on Keycloak roles
    """
    
    # URL patterns and their required permissions
    PERMISSION_MAP = {
        # Camera management URLs
        '/camera/': 'cameras',
        '/cameras/': 'cameras',
        
        # Person management URLs  
        '/alert/': 'persons',
        
        # Admin URLs (require admin role)
        '/admin/': 'admin',
    }
    
    # URLs that don't require authentication
    PUBLIC_URLS = [
        '/users/login/',
        '/oidc/',
        '/users/logout/',
        '/static/',
        '/media/',
    ]
    
    def process_request(self, request):
        """
        Process the request and check permissions
        """
        # Skip for public URLs
        path = request.path_info
        if any(path.startswith(url) for url in self.PUBLIC_URLS):
            return None
        
        # Skip if user is not authenticated
        if not request.user.is_authenticated:
            return None
        
        # Skip for superusers (they have all permissions)
        if request.user.is_superuser:
            return None
        
        # Check if user has required permissions for the URL
        required_permission = self.get_required_permission(path)
        if required_permission:
            if not self.user_has_permission(request.user, required_permission):
                logger.warning(f"User {request.user.username} denied access to {path} - missing permission: {required_permission}")
                
                # Add message and redirect
                messages.error(request, f"You don't have permission to access this resource. Required permission: {required_permission}")
                return redirect('index')
        
        return None
    
    def get_required_permission(self, path):
        """
        Get the required permission for a given URL path
        """
        for url_pattern, permission in self.PERMISSION_MAP.items():
            if path.startswith(url_pattern):
                return permission
        return None
    
    def user_has_permission(self, user, permission):
        """
        Check if user has the required permission
        """
        try:
            # Check if user has a profile
            if not hasattr(user, 'userprofile'):
                return False
            
            profile = user.userprofile
            
            # Admin permission requires superuser
            if permission == 'admin':
                return user.is_superuser
            
            # Check specific permissions
            if permission == 'cameras':
                return profile.can_access_cameras()
            elif permission == 'persons':
                return profile.can_access_persons()
            
            # Default: check if user has the permission
            return profile.has_permission(permission)
            
        except Exception as e:
            logger.error(f"Error checking permissions for user {user.username}: {str(e)}")
            return False


class KeycloakTokenRefreshMiddleware(MiddlewareMixin):
    """
    Middleware to handle Keycloak token refresh and user synchronization
    """
    
    def process_request(self, request):
        """
        Check if user needs token refresh or role synchronization
        """
        if not request.user.is_authenticated:
            return None
        
        # Skip for non-Keycloak users (fallback admin users)
        if not hasattr(request.user, 'userprofile') or not request.user.userprofile.keycloak_id:
            return None
        
        profile = request.user.userprofile
        
        # Check if user roles need to be refreshed (every hour)
        from django.utils import timezone
        from datetime import timedelta
        
        if (not profile.last_keycloak_sync or 
            timezone.now() - profile.last_keycloak_sync > timedelta(hours=1)):
            
            # Trigger role synchronization in background
            self.refresh_user_roles(request.user)
        
        return None
    
    def refresh_user_roles(self, user):
        """
        Refresh user roles from Keycloak (can be made async if needed)
        """
        try:
            from .auth_backends import KeycloakOIDCAuthenticationBackend
            
            backend = KeycloakOIDCAuthenticationBackend()
            
            # Fetch current roles from Keycloak
            if user.userprofile.keycloak_id:
                roles_dict = backend.fetch_user_roles_from_keycloak(user.userprofile.keycloak_id)
                
                # Create fake claims dict for update_user method
                fake_claims = {
                    'sub': user.userprofile.keycloak_id,
                    'email': user.email,
                    'given_name': user.first_name,
                    'family_name': user.last_name,
                }
                
                # Update user with new roles
                backend.update_user(user, fake_claims)
                
                realm_roles = roles_dict.get('realm_roles', [])
                client_roles = roles_dict.get('client_roles', [])
                logger.info(f"Refreshed roles for user {user.username}: realm={realm_roles}, client={client_roles}")
        
        except Exception as e:
            logger.error(f"Error refreshing user roles for {user.username}: {str(e)}")


def require_permission(permission):
    """
    Decorator to require specific permission for a view
    """
    def decorator(view_func):
        def wrapped_view(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return redirect('users:login')
            
            if request.user.is_superuser:
                return view_func(request, *args, **kwargs)
            
            if not hasattr(request.user, 'userprofile'):
                messages.error(request, "User profile not found")
                return redirect('index')
            
            profile = request.user.userprofile
            
            # Check permission
            has_perm = False
            if permission == 'cameras':
                has_perm = profile.can_access_cameras()
            elif permission == 'persons':
                has_perm = profile.can_access_persons()
            else:
                has_perm = profile.has_permission(permission)
            
            if not has_perm:
                messages.error(request, f"You don't have permission to access this resource. Required permission: {permission}")
                return redirect('index')
            
            return view_func(request, *args, **kwargs)
        return wrapped_view
    return decorator