from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from . import views  

urlpatterns = [
    path('', views.index, name='index'),
    path("camera/", include("cameras.urls")),
    path("alert/", include("alerts.urls")),
    path('users/', include('users.urls')),
    path("videostream/", include("videostream.urls")),
    
    # Keycloak OIDC URLs
    path('oidc/', include('mozilla_django_oidc.urls')),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
