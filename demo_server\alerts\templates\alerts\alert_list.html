{% extends 'base.html' %}

{% block content %}
<div class="alert-listing-container">
    <h2>Persons</h2>

    <form method="post" action="{% url 'alerts:delete_multiple_alerts' %}">
        {% csrf_token %}

        <div class="mb-3">
            <button type="submit" name="action" value="delete_selected" class="btn btn-warning" onclick="return confirm('Are you sure you want to delete selected persons?')">Delete Selected</button>
            <button type="submit" name="action" value="delete_all" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete ALL persons?')">Delete All</button>
        </div>

        <table class="table table-striped">
            <thead>
                <tr>
                    <th><input type="checkbox" onclick="document.querySelectorAll('.alert-checkbox').forEach(cb => cb.checked = this.checked)"></th>
                    <th>Date/Time</th>
                    <th>Camera</th>
                    <th>Recognized Person</th>
                    <th>Video Snapshot</th>
                </tr>
            </thead>
            <tbody>
                {% for alarm in page_obj %}
                <tr>
                    <td><input type="checkbox" name="selected_alerts" value="{{ alarm.id }}" class="alert-checkbox"></td>
                    <td>{{ alarm.date }}</td>
                    <td>{{ alarm.camera.name }}</td>
                    <td>{{ alarm.person.name }}</td>
                    <td>
                        {% if alarm.alert_photo %}
                            <a href="{{ alarm.alert_photo.photo.url }}" target="_blank">View Snapshot</a>
                        {% elif alarm.video_snapshot %}
                            <a href="{{ alarm.video_snapshot.url }}" target="_blank">View Snapshot</a>
                        {% else %}
                            <span class="text-muted">No photo</span>
                        {% endif %}
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6">No persons found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </form>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <nav aria-label="Page navigation">
        <ul class="pagination">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page=1">&laquo; First</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
            </li>
            {% endif %}

            {% for i in page_obj.paginator.page_range %}
                {% if page_obj.number == i %}
                <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                {% else %}
                <li class="page-item"><a class="page-link" href="?page={{ i }}">{{ i }}</a></li>
                {% endif %}
            {% endfor %}

            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last &raquo;</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

</div>

<style>
    .alert-listing-container {
        padding: 20px;
    }
    .mb-3 {
        margin-bottom: 1rem;
    }
    button[type="submit"] {
        margin-right: 10px;
    }
</style>
{% endblock %}