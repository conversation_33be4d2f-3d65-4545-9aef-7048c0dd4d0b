import os
import uuid
import logging
from django.conf import settings
from django.db import transaction
from django.core.files.base import ContentFile
from django.core.cache import cache
from django.utils import timezone
from asgiref.sync import sync_to_async
import cv2

# Import models
from alerts.models import AlertPhoto, Alarm, AlertPerson

logger = logging.getLogger(__name__)


class AlertService:
    """Service for handling alert and photo operations"""
    
    @staticmethod
    async def handle_multiple_faces(frame, camera, names, confidences):
        """
        Handle multiple face detection by creating a single photo
        Only triggers if ALL detected faces are KNOWN persons

        Args:
            frame: Video frame containing multiple faces
            camera: Camera object
            names: List of detected person names
            confidences: List of confidence scores for each person

        Returns:
            master_alarm instance or None
        """
        try:
            # Must have at least 2 faces
            if len(names) <= 1:
                return None

            # Accept ALL faces (both known and unknown) for multiple face detection
            # We want to track everyone for comprehensive monitoring
            logger.info(f"Multiple faces detected (known + unknown): {names}")
            
            # Check cooldown to prevent spam
            cache_key = f"multi_alert_cooldown_{camera.id}"
            last_alert_time = cache.get(cache_key)
            current_time = timezone.now()
            
            # Only create photo once per minute
            if last_alert_time and (current_time - last_alert_time).total_seconds() < 60:
                logger.debug(f"Multiple face cooldown active for camera {camera.id}")
                return None
            
            # Create multiple face alarms with shared video_snapshot
            master_alarm, alarm_count = await sync_to_async(AlertService._create_multiple_face_photo_and_alarms)(
                frame, camera, names, confidences
            )

            # Set cooldown
            cache.set(cache_key, current_time, 60)

            logger.info(f"Multiple faces alarms created: {alarm_count} alarms with shared video_snapshot for camera {camera.name}")
            return master_alarm
            
        except Exception as e:
            logger.error(f"Error handling multiple faces: {str(e)}")
            return None
    
    @staticmethod
    def _create_multiple_face_photo_and_alarms(frame, camera, names, confidences):
        """
        Create multiple face alarms with shared video_snapshot
        No more AlertPhoto - use shared video_snapshot for efficiency

        Args:
            frame: Video frame
            camera: Camera object
            names: List of detected person names
            confidences: List of confidence scores for each person

        Returns:
            tuple: (master_alarm, alarm_count)
        """
        try:
            filename = f"multiple_faces_{uuid.uuid4()}.jpg"
            
            # Encode frame to JPEG
            _, buffer = cv2.imencode('.jpg', frame)
            content = ContentFile(buffer.tobytes())

            with transaction.atomic():
                # Create alarms for ALL persons with shared video_snapshot
                master_alarm = None
                alarm_count = 0
                
                for name, confidence in zip(names, confidences):
                    try:
                        person = AlertPerson.objects.filter(name=name).first()
                        if person:
                            # Create alarm with individual video_snapshot
                            alarm = Alarm.objects.create(
                                person=person,
                                camera=camera,
                                confidence=confidence
                                # video_snapshot will be set below
                            )
                            
                            if master_alarm is None:
                                # First alarm - save the actual file
                                alarm.video_snapshot.save(filename, content, save=True)
                                master_alarm = alarm
                                master_file_path = alarm.video_snapshot.name
                                logger.debug(f"Created master alarm with video_snapshot: {master_file_path}")
                            else:
                                # Subsequent alarms - reference the same file
                                alarm.video_snapshot.name = master_file_path
                                alarm.save()
                                logger.debug(f"Created shared alarm referencing: {master_file_path}")
                            
                            alarm_count += 1
                            person_type = "unknown" if person.is_unknown else "known"
                            logger.debug(f"Created alarm for {person_type} person: {name} (confidence: {confidence:.2f}) in multiple face detection")
                        else:
                            logger.warning(f"Person not found in database: {name}")
                    except Exception as e:
                        logger.error(f"Error creating alarm for {name}: {str(e)}")

            return master_alarm, alarm_count

        except Exception as e:
            logger.error(f"Error creating multiple face alarms: {str(e)}")
            raise
