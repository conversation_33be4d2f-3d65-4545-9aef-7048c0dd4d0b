{% load static %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Face Recognition System{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="{% static 'common/css/bootstrap.min.css' %}" rel="stylesheet">
     
    <!-- Bootstrap Icons -->
    <link href="{% static 'common/css/bootstrap-icons.css' %}" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="{% static 'common/css/inter-font.css' %}" rel="stylesheet">
    
    <!-- AOS Animation Library -->
    <link href="{% static 'common/css/aos.css' %}" rel="stylesheet">
    
    <link rel="stylesheet" href="{% static 'common/css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'common/css/index.css' %}">
    <link rel="stylesheet" href="{% static 'alerts/css/alerts.css' %}">
    <link rel="stylesheet" href="{% static 'cameras/css/cameras.css' %}">
    
</head>
<body class="bg-light" data-bs-theme="light">
<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-nav bg-gradient shadow-sm sticky-top" style="padding: 1rem 0;">
    <div class="container">
        <a class="navbar-brand fw-bold d-flex align-items-center fs-1" href="{% url 'index' %}">
            <i class="bi bi-person-bounding-box me-2"></i>
            Face Recognition
        </a>
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link fs-5" href="{% url 'index' %}">
                        <i class="bi bi-house-fill me-2"></i>Home
                    </a>
                </li>
                {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle fs-5" href="#" id="cameraDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-camera-video-fill me-2"></i>Cameras
                        </a>
                        <ul class="dropdown-menu shadow">
                            <li><a class="dropdown-item" href="{% url 'cameras:index' %}">
                                <i class="bi bi-list-ul me-2"></i>Camera List
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'cameras:add' %}">
                                <i class="bi bi-plus-circle me-2"></i>Add Camera
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle fs-5" href="#" id="alertDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-people-fill me-2"></i>Persons
                        </a>
                        <ul class="dropdown-menu shadow">
                            <li><a class="dropdown-item" href="{% url 'alerts:index' %}">
                                <i class="bi bi-grid-3x3-gap-fill me-2"></i>Person Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'alerts:person_list' %}">
                                <i class="bi bi-person-check-fill me-2"></i>Person List
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'alerts:alert_list' %}">
                                <i class="bi bi-bell-fill me-2"></i>Alert Persons
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'alerts:unknown_persons_list' %}">
                                <i class="bi bi-person-exclamation me-2"></i>Unknown Persons
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="venueDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-building me-1"></i>Venues
                        </a>
                        <ul class="dropdown-menu shadow">
                            <li><a class="dropdown-item" href="{% url 'alerts:venue_list' %}">
                                <i class="bi bi-list-ul me-2"></i>Venue List
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'alerts:venue_create' %}">
                                <i class="bi bi-plus-circle me-2"></i>Add Venue
                            </a></li>
                        </ul>
                    </li>
                {% endif %}
            </ul>
            <ul class="navbar-nav">
                <!-- Theme Toggle Button -->
                <li class="nav-item">
                    <button class="btn btn-outline-warning shadow fs-4 me-3" id="theme-toggle" title="Toggle Theme">
                        <i class="bi bi-sun-fill" id="theme-icon"></i>
                    </button>
                </li>
                {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle fs-5 d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-person-circle me-1"></i>{{ user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow">
                            <li>
                                <form method="post" action="{% url 'users:logout' %}" class="m-0">
                                    {% csrf_token %}
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="bi bi-box-arrow-right me-2"></i>Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'oidc_authentication_init' %}">
                            <i class="bi bi-box-arrow-in-right me-1"></i>Login with Keycloak
                        </a>
                    </li>
                {% endif %}
            </ul>
        </div>
    </div>
</nav>
<!-- Toast Messages Container -->
<div class="toast-container position-fixed bottom-0 end-0 p-3" id="toast-container">
    {% if messages %}
        {% for message in messages %}
            <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="true" data-bs-delay="5000">
                <div class="toast-header bg-{{ message.tags }} text-white border-0">
                    <i class="bi bi-{% if message.tags == 'success' %}check-circle-fill{% elif message.tags == 'error' %}exclamation-triangle-fill{% elif message.tags == 'warning' %}exclamation-circle-fill{% else %}info-circle-fill{% endif %} me-2"></i>
                    <strong class="me-auto">
                        {% if message.tags == 'success' %}Success
                        {% elif message.tags == 'error' %}Error  
                        {% elif message.tags == 'warning' %}Warning
                        {% else %}Info
                        {% endif %}
                    </strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    {{ message }}
                </div>
            </div>
        {% endfor %}
    {% endif %}
</div>

<!-- Main Content Container -->
<main class="container-fluid py-4">
    {% block content %}
    <!-- Default content can be added here if needed, but typically should be empty -->
    {% endblock %}
</main>


    <!-- Bootstrap JS Bundle with Popper -->
    <script src="{% static 'common/js/bootstrap.bundle.min.js' %}"></script>

    <!-- AOS Animation Library -->
    <script src="{% static 'common/js/aos.js' %}"></script>

    <!-- Modular JavaScript Files -->
    <script src="{% static 'common/js/theme_manager.js' %}"></script>
    <script src="{% static 'common/js/toast_manager.js' %}"></script>
    <script src="{% static 'common/js/app_initializer.js' %}"></script>
</body>
</html>