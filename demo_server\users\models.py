from django.db import models
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
import json

# Create your models here.

class UserProfile(models.Model):
    RECOGNIZER_CHOICES = [
        ('facenet', 'FaceNetRecognizer'),
    ]
    
    # Permission choices for staff roles
    PERMISSION_CHOICES = [
        ('cameras', 'Camera Management'),
        ('persons', 'Person Management'),
        ('all', 'All Permissions'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    preferred_recognizer = models.CharField(
        max_length=20,
        choices=RECOGNIZER_CHOICES,
        default='facenet',
    )
    is_organizational_admin = models.BooleanField(default=False)
    
    # Keycloak integration fields
    keycloak_id = models.CharField(max_length=255, unique=True, null=True, blank=True)
    keycloak_realm_roles = models.TextField(blank=True, default='[]', help_text='JSON list of Keycloak realm roles')
    keycloak_client_roles = models.TextField(blank=True, default='[]', help_text='JSON list of Keycloak client roles')
    permissions = models.TextField(blank=True, default='[]', help_text='JSON list of user permissions')
    last_keycloak_sync = models.DateTimeField(null=True, blank=True)
    
    def __str__(self):
        return self.user.username
    
    def get_keycloak_realm_roles(self):
        """Get list of Keycloak realm roles"""
        try:
            return json.loads(self.keycloak_realm_roles)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def set_keycloak_realm_roles(self, roles):
        """Set list of Keycloak realm roles"""
        self.keycloak_realm_roles = json.dumps(roles)
    
    def get_keycloak_client_roles(self):
        """Get list of Keycloak client roles"""
        try:
            return json.loads(self.keycloak_client_roles)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def set_keycloak_client_roles(self, roles):
        """Set list of Keycloak client roles"""
        self.keycloak_client_roles = json.dumps(roles)
    
    def get_permissions(self):
        """Get list of user permissions"""
        try:
            return json.loads(self.permissions)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def set_permissions(self, perms):
        """Set list of user permissions"""
        self.permissions = json.dumps(perms)
    
    def has_permission(self, permission):
        """Check if user has a specific permission"""
        user_perms = self.get_permissions()
        return 'all' in user_perms or permission in user_perms
    
    def can_access_cameras(self):
        """Check if user can access camera management"""
        return self.has_permission('cameras')
    
    def can_access_persons(self):
        """Check if user can access person management"""
        return self.has_permission('persons')
    
    def update_from_keycloak(self, user_info, roles_dict):
        """Update user profile based on Keycloak user info and roles"""
        from django.utils import timezone
        
        # Update basic user information
        if 'email' in user_info:
            self.user.email = user_info['email']
        if 'given_name' in user_info:
            self.user.first_name = user_info['given_name']
        if 'family_name' in user_info:
            self.user.last_name = user_info['family_name']
        
        # Store Keycloak ID
        if 'sub' in user_info:
            self.keycloak_id = user_info['sub']
        
        # Update realm and client roles
        realm_roles = roles_dict.get('realm_roles', [])
        client_roles = roles_dict.get('client_roles', [])
        
        self.set_keycloak_realm_roles(realm_roles)
        self.set_keycloak_client_roles(client_roles)
        self._update_permissions_from_roles(realm_roles, client_roles)
        
        # Update Django user flags based on realm roles
        self.user.is_staff = 'django-admin' in realm_roles or 'django-staff' in realm_roles
        self.user.is_superuser = 'django-admin' in realm_roles
        
        # Update organizational admin flag
        self.is_organizational_admin = 'django-admin' in realm_roles
        
        # Update sync timestamp
        self.last_keycloak_sync = timezone.now()
        
        # Save both user and profile
        self.user.save()
        self.save()
    
    def _update_permissions_from_roles(self, realm_roles, client_roles):
        """Update permissions based on Keycloak realm and client roles"""
        from django.conf import settings
        
        permissions = set()
        
        # Check realm roles for permissions
        for role in realm_roles:
            if role in settings.KEYCLOAK_REALM_ROLES:
                role_config = settings.KEYCLOAK_REALM_ROLES[role]
                permissions.update(role_config.get('permissions', []))
        
        # Check client roles for permissions
        for role in client_roles:
            if role in settings.KEYCLOAK_CLIENT_ROLES:
                role_permissions = settings.KEYCLOAK_CLIENT_ROLES[role]
                permissions.update(role_permissions)
        
        self.set_permissions(list(permissions))

