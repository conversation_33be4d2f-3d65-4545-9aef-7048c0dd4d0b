from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from . import views

app_name = 'alerts'

urlpatterns = [
    path('', views.index, name='index'),
    path('delete-multiple-alerts/', views.delete_multiple_alerts, name='delete_multiple_alerts'),
    path('persons/', views.person_list, name='person_list'),
    path('persons/delete-selected/', views.delete_selected_persons, name='delete_selected_persons'),
    path('person/add/', views.add_alert_person, name='add_person'),
    path('person/<int:person_id>/', views.person_detail, name='person_detail'),
    path('person/<int:person_id>/times/', views.person_times, name='person_times'),
    path('photos/delete/<int:photo_id>/', views.delete_photo, name='delete_photo'),
    path('alerts/', views.alert_list, name='alert_list'),
    path('unknown_persons/', views.unknown_persons_list, name='unknown_persons_list'),
    path('unknown_persons/<int:person_id>/', views.unknown_person_detail, name='unknown_person_detail'),
    path('unknown_persons/<int:person_id>/rename/', views.rename_unknown_person, name='rename_unknown_person'),
    
    # Venue Management URLs
    path('venues/', views.venue_list, name='venue_list'),
    path('venues/create/', views.venue_create, name='venue_create'),
    path('venues/<int:venue_id>/edit/', views.venue_edit, name='venue_edit'),
    path('venues/<int:venue_id>/delete/', views.venue_delete, name='venue_delete'),
]