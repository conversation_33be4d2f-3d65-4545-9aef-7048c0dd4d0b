"""
Django management command to create initial admin user with Keycloak integration
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from users.models import UserProfile


class Command(BaseCommand):
   help = 'Create emergency admin user for local authentication fallback'

    def add_arguments(self, parser):
        parser.add_argument('--username', type=str, help='Admin username', default='admin')
        parser.add_argument('--email', type=str, help='Admin email', default='admin@localhost')
        parser.add_argument('--password', type=str, help='Admin password', required=True)

    def handle(self, *args, **options):
        username = options['username']
        email = options['email'] 
        password = options['password']
        
        try:
            # Check if admin user already exists
            if User.objects.filter(username=username).exists():
                self.stdout.write(
                    self.style.WARNING(f'User {username} already exists')
                )
                return
            
            # Create admin user
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                is_staff=True,
                is_superuser=True
            )
            
            # Create user profile
            UserProfile.objects.create(
                user=user,
                preferred_recognizer='facenet',
                is_organizational_admin=True
            )
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created emergency admin user: {username}\n'
                    f'This user can be used for local authentication when Keycloak is unavailable.\n'
                    f'Note: Regular users should authenticate through Keycloak.'
                )
            )
            
        except Exception as e:
            self.stderr.write(f"Error creating admin user: {str(e)}")