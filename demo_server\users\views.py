import numpy as np
from PIL import Image
from django.core.files.base import ContentFile
from io import BytesIO
from django.utils import timezone

from django.http import HttpResponse, HttpResponseForbidden, JsonResponse
from django.contrib.auth.models import User
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.urls import reverse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
from django.contrib.auth import logout as django_logout
from django.http import HttpResponseRedirect
from urllib.parse import urlencode

from .forms import is_admin_user

from .models import UserProfile
from .middleware import require_permission


import logging
logger = logging.getLogger(__name__)


@require_http_methods(["POST"])
@csrf_exempt
def sync_user_roles(request):
    """API endpoint to manually sync user roles from Keycloak"""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)
    
    if not hasattr(request.user, 'userprofile') or not request.user.userprofile.keycloak_id:
        return JsonResponse({'error': 'User is not managed by Keycloak'}, status=400)
    
    try:
        from .auth_backends import KeycloakOIDCAuthenticationBackend
        
        backend = KeycloakOIDCAuthenticationBackend()
        
        # Fetch current roles from Keycloak
        roles_dict = backend.fetch_user_roles_from_keycloak(request.user.userprofile.keycloak_id)
        
        # Create fake claims dict for update_user method
        fake_claims = {
            'sub': request.user.userprofile.keycloak_id,
            'email': request.user.email,
            'given_name': request.user.first_name,
            'family_name': request.user.last_name,
        }
        
        # Update user with new roles
        backend.update_user(request.user, fake_claims)
        
        return JsonResponse({
            'success': True,
            'realm_roles': roles_dict.get('realm_roles', []),
            'client_roles': roles_dict.get('client_roles', []),
            'permissions': request.user.userprofile.get_permissions()
        })
    
    except Exception as e:
        logger.error(f"Error syncing user roles: {str(e)}")
        return JsonResponse({'error': 'Failed to sync roles'}, status=500)


def keycloak_logout_callback(request):
    """Handle logout callback from Keycloak"""
    messages.success(request, 'You have been successfully logged out.')
    return redirect('index')

def keycloak_logout(request):
    # 1) id_token'ı session'dan al (Logout'tan önce!)
    id_token = request.session.get('oidc_id_token')

    # 2) Önce Django oturumunu kapat
    django_logout(request)

    # 3) Keycloak end-session URL'ini hazırla
    #    post_logout_redirect_uri: Keycloak'tan döndüğünde gideceğin URL
    post_logout_redirect_uri = request.build_absolute_uri(reverse('users:keycloak_logout_callback'))

    # Keycloak standard end-session endpoint:
    end_session_url = (
        f"{settings.KEYCLOAK_SERVER_URL}/realms/{settings.KEYCLOAK_REALM}/protocol/openid-connect/logout"
    )

    params = {
        'post_logout_redirect_uri': post_logout_redirect_uri,
    }

    # id_token varsa Keycloak'a ipucu olarak gönder (tavsiye edilir)
    if id_token:
        params['id_token_hint'] = id_token

    return HttpResponseRedirect(f"{end_session_url}?{urlencode(params)}")