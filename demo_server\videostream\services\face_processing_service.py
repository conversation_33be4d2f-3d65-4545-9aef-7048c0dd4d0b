import logging
import uuid
from django.core.cache import cache
from django.core.files.base import ContentFile
from django.utils import timezone
from asgiref.sync import sync_to_async
from channels.layers import get_channel_layer
import cv2

# Import our services
from .recognition_service import RecognitionService
from .zone_service import ZoneService
from .alert_service import AlertService
from .person_service import PersonService
from .drawing_service import DrawingService

# Import models
from alerts.models import Alarm, AlertPerson

logger = logging.getLogger(__name__)


class FaceProcessingService:
    """Main orchestrator service for face processing pipeline"""
    
    def __init__(self, camera_id=None):
        self.camera_id = camera_id
        self.person_service = PersonService()
    
    async def process_frame(self, frame, camera, filter_by_zone=False, zones=None):
        """
        Main processing pipeline for face recognition
        
        Args:
            frame: Input video frame
            camera: Camera object
            filter_by_zone: Whether to apply zone filtering
            zones: List of zone definitions
            
        Returns:
            Processed frame with annotations
        """
        try:
            print(f"🎥 PROCESS_FRAME - Camera: {camera.name}, Type: {camera.camera_type}")
            
            # Step 1: Face Recognition
            bboxes, names, confidences = await RecognitionService.detect_faces(frame, camera.id)
            
            if bboxes is None or len(bboxes) == 0:
                return frame
                
            print(f"👤 FACES_DETECTED - Count: {len(names)}, Names: {names}")
            
            # Step 2: Zone Filtering (if enabled)
            if filter_by_zone and zones:
                bboxes, names, confidences = ZoneService.filter_detections_by_zones(
                    bboxes, names, confidences, zones, frame.shape
                )
                
                if len(bboxes) == 0:
                    return frame
            
            # Step 3: Pre-process unknown persons for multiple face detection
            processed_names = await self._preprocess_unknown_persons(names, frame, camera)
            
            # Step 4: Handle Multiple Faces (for both known and unknown persons)
            master_alarm = await AlertService.handle_multiple_faces(frame, camera, processed_names, confidences)
            
            # Step 5: Process Individual Detections
            final_names = await self._process_individual_detections(
                bboxes, processed_names, confidences, frame, camera, master_alarm
            )
            
            # Step 6: Draw Annotations
            annotated_frame = DrawingService.annotate_frame(frame, bboxes, final_names, confidences)
            
            return annotated_frame
            
        except Exception as e:
            logger.error(f"Error in face processing pipeline: {str(e)}")
            return frame
    
    async def _process_individual_detections(self, bboxes, names, confidences, frame, camera, master_alarm):
        """
        Process each detected face individually
        
        Args:
            bboxes: Face bounding boxes
            names: Detected names
            confidences: Confidence scores
            frame: Video frame
            camera: Camera object
            master_alarm: Master alarm from multiple face detection (if any)
            
        Returns:
            List of processed names (may include newly created unknown persons)
        """
        processed_names = []
        
        for bbox, name, confidence in zip(bboxes, names, confidences):
            try:
                display_name = name
                
                # Get person from database (works for both known and unknown)
                if name.startswith("Unknown_"):
                    # Unknown person (already created in preprocessing)
                    person = await sync_to_async(AlertPerson.objects.filter(name=name, is_unknown=True).first)()
                elif name != "Unknown":  # Known person
                    # Update last seen date for known persons
                    person = await PersonService.update_person_last_seen(name)
                else:
                    # Fallback for generic "Unknown" (shouldn't happen after preprocessing)
                    person = None
                    logger.warning(f"Generic 'Unknown' detected - this shouldn't happen after preprocessing")
                
                # Create alarm for both known and unknown persons (if not multiple face)
                if person and not master_alarm:  # Skip if multiple face already handled it
                    alert_result = await self._create_person_alert(
                        camera, display_name, confidence, frame, None, person
                    )
                    
                    # Send WebSocket notification
                    if alert_result:
                        await self._send_websocket_notification(alert_result)
                elif person and master_alarm:
                    # Multiple face case - alarm already created, just log
                    person_type = "unknown" if person.is_unknown else "known"
                    logger.debug(f"Skipping individual alarm for {person_type} person {name} - handled by multiple face")
                
                processed_names.append(display_name)
                
            except Exception as e:
                logger.error(f"Error processing individual detection: {str(e)}")
                processed_names.append(name)  # Fallback to original name
        
        return processed_names

    async def _preprocess_unknown_persons(self, names, frame, camera):
        """
        Pre-process unknown persons to ensure they exist in database
        This is needed for multiple face detection to work properly
        
        Args:
            names: List of detected names
            frame: Video frame  
            camera: Camera object
            
        Returns:
            List of processed names (Unknown -> Unknown_X)
        """
        processed_names = []
        
        for name in names:
            if name == "Unknown":
                # Create unknown person in database
                alert_person, new_name = await self.person_service.process_unknown_person(frame, camera)
                if alert_person and new_name:
                    processed_names.append(new_name)  # Unknown_X
                    logger.debug(f"Pre-processed unknown person: {new_name}")
                else:
                    processed_names.append("Unknown")  # Fallback
                    logger.warning("Failed to pre-process unknown person")
            else:
                processed_names.append(name)  # Keep known names as-is
                
        return processed_names

    async def _create_person_alert(self, camera, name, confidence, frame, master_photo, person):
        """
        Create visit tracking record based on camera type and person status
        
        Smart logic:
        - Entry camera: Only record ENTRY if person hasn't entered or has exited
        - Exit camera: Only record EXIT if person has entered and hasn't exited
        - BOTH camera: Record based on last visit type

        Args:
            camera: Camera object
            name: Person name
            confidence: Recognition confidence
            frame: Video frame
            master_photo: Multiple face photo (if any)
            person: AlertPerson instance

        Returns:
            Alert result dict or None
        """
        try:
            print(f"🚨 CREATE_PERSON_ALERT - Person: {person.name}, Camera: {camera.name}")
            
            # Check if we should record this alarm based on camera type and person status
            should_record_alarm = await self._should_record_alarm(person, camera)
            
            print(f"🚨 SHOULD_RECORD_RESULT - {should_record_alarm}")
            
            if not should_record_alarm:
                print(f"⏭️ SKIPPING ALARM - {person.name} on {camera.name}")
                logger.debug(f"Skipping alarm for {person.name} on {camera.name} - already in correct state")
                return None

            # Determine alarm type based on camera type
            alarm_type = await self._determine_alarm_type(camera, person)
            print(f"🚨 ALARM_TYPE - {alarm_type}")
            
            if not alarm_type:
                print(f"❌ NO ALARM TYPE - Camera: {camera.name}")
                logger.warning(f"Could not determine alarm type for camera {camera.name}")
                return None

            print(f"💾 CREATING ALARM - Type: {alarm_type}, Person: {person.name}")
            
            # Create alarm record
            alarm_result = await self._create_alarm_async(
                person, camera, confidence, frame, master_photo, alarm_type
            )

            if alarm_result:
                print(f"✅ ALARM CREATED - {alarm_type} for {person.name} on {camera.name}")
                logger.info(f"✅ Created {alarm_type} alarm for {person.name} on camera {camera.name} "
                          f"(confidence: {confidence:.2f})")

                # Return alert result for WebSocket notification
                return {
                    "user_id": person.user.id,
                    "name": person.name,
                    "camera_name": camera.name,
                    "confidence": confidence,
                    "alarm_type": alarm_type
                }
            else:
                print(f"❌ ALARM CREATION FAILED - {person.name}")
                logger.error(f"❌ Failed to create alarm for {person.name}")
                return None

        except Exception as e:
            logger.error(f"Error creating person alarm: {str(e)}")
            return None

    async def _should_record_alarm(self, person, camera):
        """
        VENUE-BASED: Determine if we should record an alarm based on person's status in that venue
        Uses PersonVenueStatus model for reliable state tracking
        
        Args:
            person: AlertPerson instance
            camera: Camera object
            
        Returns:
            bool: True if alarm should be recorded
        """
        try:
            print(f"🔍 SHOULD_RECORD_ALARM - Person: {person.name}, Camera: {camera.name}, Type: {camera.camera_type}")
            
            # Eğer kameranın venue'si yoksa, entry/exit kaydetme
            if not camera.venue:
                print(f"⚠️ NO VENUE - Camera {camera.name} has no venue - SKIPPING")
                logger.warning(f"⚠️ Camera {camera.name} has no venue - SKIPPING entry/exit tracking for {person.name}")
                return False
            
            print(f"📍 VENUE CHECK - Camera: {camera.name}, Venue: {camera.venue.name}")
            
            # Import PersonVenueStatus here to avoid circular imports
            from alerts.models import PersonVenueStatus
            
            # Get or create person venue status using sync_to_async
            def get_venue_status():
                return PersonVenueStatus.get_or_create_status(person, camera.venue)
            
            venue_status, created = await sync_to_async(get_venue_status)()
            
            if created:
                print(f"🆕 NEW VENUE STATUS - Created status for {person.name} in {camera.venue.name}: {venue_status.status}")
                logger.info(f"🆕 Created new venue status for {person.name} in {camera.venue.name}: {venue_status.status}")
            
            current_status = venue_status.status
            print(f"📊 CURRENT STATUS - {person.name} in {camera.venue.name}: {current_status}")
            logger.info(f"📊 Current venue status: {person.name} in {camera.venue.name} = {current_status}")
            
            # SMART LOGIC: Only record alarms when there's a state change
            if camera.camera_type == 'ENTRY':
                # Entry camera: Only record if person is currently OUTSIDE
                should_record = (current_status == 'OUTSIDE')
                print(f"🚪 ENTRY LOGIC - Current: {current_status}, Should record: {should_record}")
                logger.info(f"🔍 ENTRY Check: {person.name} - current_status={current_status}, should_record={should_record}")
                if should_record:
                    print(f"✅ ENTRY ALLOWED - {person.name} entering {camera.venue.name} (was OUTSIDE)")
                    logger.info(f"✅ ENTRY: {person.name} entering {camera.venue.name} (transition: OUTSIDE→INSIDE)")
                else:
                    print(f"⏭️ ENTRY BLOCKED - {person.name} already INSIDE {camera.venue.name} - no duplicate entry")
                    logger.info(f"⏭️ Blocked duplicate ENTRY: {person.name} already INSIDE {camera.venue.name}")
                return should_record
                
            elif camera.camera_type == 'EXIT':
                # Exit camera: Only record if person is currently INSIDE
                should_record = (current_status == 'INSIDE')
                print(f"🚪 EXIT LOGIC - Current: {current_status}, Should record: {should_record}")
                logger.info(f"🔍 EXIT Check: {person.name} - current_status={current_status}, should_record={should_record}")
                if should_record:
                    print(f"✅ EXIT ALLOWED - {person.name} leaving {camera.venue.name} (was INSIDE)")
                    logger.info(f"✅ EXIT: {person.name} leaving {camera.venue.name} (transition: INSIDE→OUTSIDE)")
                else:
                    print(f"⏭️ EXIT BLOCKED - {person.name} already OUTSIDE {camera.venue.name} - no duplicate exit")
                    logger.info(f"⏭️ Blocked duplicate EXIT: {person.name} already OUTSIDE {camera.venue.name}")
                return should_record
                
            print(f"❌ UNKNOWN CAMERA TYPE - {camera.camera_type}")
            return False
            
        except Exception as e:
            print(f"❌ ERROR in _should_record_alarm: {str(e)}")
            logger.error(f"Error checking alarm status: {str(e)}")
            return False  # Default to NOT allowing alarm on error to prevent spam



    async def _determine_alarm_type(self, camera, person):
        """
        VENUE-BASED: Determine what type of alarm to record based on camera type
        
        Args:
            camera: Camera object
            person: AlertPerson instance
            
        Returns:
            str: 'ENTRY' or 'EXIT' or None if cannot determine
        """
        try:
            if camera.camera_type == 'ENTRY':
                return 'ENTRY'
            elif camera.camera_type == 'EXIT':
                return 'EXIT'
            # BOTH kamera tipi artık yok
            
            return None
            
        except Exception as e:
            logger.error(f"Error determining alarm type: {str(e)}")
            return None

    async def _create_alarm_async(self, person, camera, confidence, frame, master_photo, alarm_type=None):
        """
        Async method to create alarm in database and update venue status
        Using sync_to_async for database operations

        Args:
            person: AlertPerson instance
            camera: Camera object
            confidence: Recognition confidence
            frame: Video frame
            master_photo: Multiple face photo (if any)
            alarm_type: Type of alarm (ENTRY, EXIT, DETECTION)

        Returns:
            bool: True if alarm created successfully
        """
        try:
            print(f"💾 _CREATE_ALARM_ASYNC - Starting alarm creation for {person.name}")
            
            # alarm_type None ise alarm oluşturma
            if alarm_type is None:
                print(f"❌ ALARM_TYPE_NULL - No alarm type provided, skipping alarm creation")
                logger.warning(f"Alarm type is None for {person.name} on camera {camera.name}, skipping alarm creation")
                return False
            
            from alerts.models import Alarm, PersonVenueStatus
            import uuid
            import cv2
            from django.core.files.base import ContentFile

            # Create alarm with alarm_type using sync_to_async
            def create_alarm_and_update_status():
                print(f"💾 DB_OPERATION - Creating Alarm object")
                # Venue snapshot'ı ekle - alarm oluşturulduğu andaki venue adını sakla
                venue_name = camera.venue.name if camera.venue else "Venue Atanmamış"
                
                alarm = Alarm(
                    person=person,
                    camera=camera,
                    alarm_type=alarm_type,
                    confidence=confidence,
                    venue_name=venue_name
                )
                
                print(f"💾 IMAGE_SAVE - Encoding frame")
                # Always save individual video_snapshot
                _, buffer = cv2.imencode('.jpg', frame)
                content = ContentFile(buffer.tobytes())
                alarm.video_snapshot.save(
                    f'{person.name}_snapshot_{uuid.uuid4()}.jpg',
                    content
                )
                
                print(f"💾 DB_SAVE - Saving to database")
                # Save alarm to database
                alarm.save()
                print(f"💾 DB_SUCCESS - Alarm saved with ID: {alarm.id}")
                
                # Update venue status if this is an ENTRY/EXIT alarm
                if alarm_type in ['ENTRY', 'EXIT'] and camera.venue:
                    print(f"📊 STATUS_UPDATE - Updating venue status for {person.name}")
                    venue_status, created = PersonVenueStatus.get_or_create_status(person, camera.venue)
                    
                    new_status = 'INSIDE' if alarm_type == 'ENTRY' else 'OUTSIDE'
                    old_status = venue_status.status
                    
                    venue_status.update_status(new_status, camera)
                    print(f"📊 STATUS_CHANGED - {person.name} in {camera.venue.name}: {old_status} → {new_status}")
                    logger.info(f"📊 Venue status updated: {person.name} in {camera.venue.name}: {old_status} → {new_status}")
                
                return alarm

            alarm = await sync_to_async(create_alarm_and_update_status)()
            print(f"💾 ASYNC_SUCCESS - Alarm created successfully: ID={alarm.id}")
            logger.debug(f"Alarm saved to database: Alarm.id={alarm.id}")
            return True

        except Exception as e:
            print(f"❌ DB_ERROR - Failed to create alarm: {str(e)}")
            logger.error(f"Failed to create alarm in database: {str(e)}")
            return False

    async def _send_websocket_notification(self, alert_result):
        """
        Send WebSocket notification for alert

        Args:
            alert_result: Alert result dictionary
        """
        try:
            channel_layer = get_channel_layer()
            await channel_layer.group_send(
                f"user_{alert_result['user_id']}",
                {
                    "type": "send_alert",
                    "message": f"Alert: {alert_result['name']} detected on camera {alert_result['camera_name']} with confidence {alert_result['confidence']:.2f}"
                }
            )
            logger.debug(f"WebSocket notification sent for {alert_result['name']}")

        except Exception as e:
            logger.error(f"Error sending WebSocket notification: {str(e)}")

    @classmethod
    def cleanup(cls):
        """Cleanup service resources"""
        try:
            RecognitionService.cleanup()
            logger.info("Face processing service cleaned up")
        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")
