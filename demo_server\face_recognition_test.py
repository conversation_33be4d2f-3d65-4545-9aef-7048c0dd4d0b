#!/usr/bin/env python
"""
Face Recognition Test Script for Camera ID 3
Bu script test görsellerini kamera ID 3'ün işleme algorit<PERSON><PERSON>na do<PERSON> gönderir
ve her görsel için güven skorları ile tanıma durumunu kaydeder.
"""

import os
import sys
import django
import cv2
import asyncio
import logging
from datetime import datetime
import json

# Django setup
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "web_server.settings")
django.setup()

from cameras.models import Camera
from videostream.face_recognition import FaceRecognitionProcessor
from videostream.services.recognition_service import RecognitionService

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FaceRecognitionTester:
    def __init__(self, camera_id=3):
        self.camera_id = camera_id
        self.test_images_dir = "test_images"
        self.results = []
        
    async def setup_camera(self):
        """Kamera nesnesini hazırla veya oluştur"""
        try:
            self.camera = await self._get_or_create_camera()
            logger.info(f"Camera setup completed for ID: {self.camera_id}")
        except Exception as e:
            logger.error(f"Error setting up camera: {e}")
            raise
    
    async def _get_or_create_camera(self):
        """Kamera ID 3'ü al veya oluştur"""
        from asgiref.sync import sync_to_async
        
        try:
            camera = await sync_to_async(Camera.objects.get)(id=self.camera_id)
            logger.info(f"Found existing camera: {camera.name}")
            return camera
        except Camera.DoesNotExist:
            # Kamera yoksa test için oluştur
            logger.info(f"Creating test camera with ID {self.camera_id}")
            from django.contrib.auth.models import User
            
            # İlk kullanıcıyı al veya test kullanıcısı oluştur
            try:
                user = await sync_to_async(User.objects.first)()
                if not user:
                    user = await sync_to_async(User.objects.create_user)(
                        username='testuser', 
                        password='testpass'
                    )
            except Exception as e:
                logger.error(f"Error getting/creating user: {e}")
                raise
            
            camera = await sync_to_async(Camera.objects.create)(
                id=self.camera_id,
                user=user,
                name=f"Test Camera {self.camera_id}",
                rtsp_url="test://localhost",
                camera_type="ENTRY"
            )
            logger.info(f"Created test camera: {camera.name}")
            return camera
    
    async def load_test_images(self):
        """Test görsellerini yükle"""
        test_images = []
        
        if not os.path.exists(self.test_images_dir):
            logger.error(f"Test images directory not found: {self.test_images_dir}")
            return test_images
        
        # Desteklenen görsel formatları
        supported_formats = ('.jpg', '.jpeg', '.png', '.bmp')
        
        for filename in os.listdir(self.test_images_dir):
            if filename.lower().endswith(supported_formats):
                image_path = os.path.join(self.test_images_dir, filename)
                
                # Görseli oku
                image = cv2.imread(image_path)
                if image is not None:
                    test_images.append({
                        'filename': filename,
                        'path': image_path,
                        'image': image,
                        'shape': image.shape
                    })
                    logger.info(f"Loaded test image: {filename} - Shape: {image.shape}")
                else:
                    logger.warning(f"Could not load image: {filename}")
        
        logger.info(f"Total test images loaded: {len(test_images)}")
        return test_images
    
    async def test_single_image(self, image_data):
        """Tek bir görseli test et"""
        filename = image_data['filename']
        image = image_data['image']
        
        logger.info(f"\n{'='*50}")
        logger.info(f"Testing image: {filename}")
        logger.info(f"Image shape: {image.shape}")
        
        try:
            # Yüz tanıma işlemini çalıştır
            bboxes, names, confidences = await RecognitionService.detect_faces(
                image, self.camera_id
            )
            
            # Sonuçları işle
            result = {
                'filename': filename,
                'timestamp': datetime.now().isoformat(),
                'image_shape': image.shape,
                'faces_detected': 0,
                'faces': [],
                'success': False,
                'error': None
            }
            
            if bboxes is not None and len(bboxes) > 0:
                result['faces_detected'] = len(bboxes)
                result['success'] = True
                
                for i, (bbox, name, confidence) in enumerate(zip(bboxes, names, confidences)):
                    face_info = {
                        'face_index': i + 1,
                        'name': name,
                        'confidence': float(confidence) if confidence is not None else None,
                        'bbox': bbox.tolist() if hasattr(bbox, 'tolist') else str(bbox),
                        'recognition_status': 'SUCCESS' if name != 'Unknown' else 'UNKNOWN'
                    }
                    result['faces'].append(face_info)
                    
                    logger.info(f"  Face {i+1}:")
                    logger.info(f"    Name: {name}")
                    logger.info(f"    Confidence: {confidence}")
                    logger.info(f"    Status: {face_info['recognition_status']}")
            else:
                logger.info("  No faces detected")
                result['success'] = True  # No error, just no faces
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing image {filename}: {e}")
            return {
                'filename': filename,
                'timestamp': datetime.now().isoformat(),
                'image_shape': image.shape,
                'faces_detected': 0,
                'faces': [],
                'success': False,
                'error': str(e)
            }
    
    async def run_test_suite(self):
        """Tüm test görsellerini çalıştır"""
        logger.info("Starting Face Recognition Test Suite")
        logger.info(f"Target Camera ID: {self.camera_id}")
        
        # Kamerayı hazırla
        await self.setup_camera()
        
        # Test görsellerini yükle
        test_images = await self.load_test_images()
        
        if not test_images:
            logger.error("No test images found!")
            return
        
        # Her görseli test et
        for image_data in test_images:
            result = await self.test_single_image(image_data)
            self.results.append(result)
        
        # Sonuçları kaydet ve özetle
        await self.save_results()
        self.print_summary()
    
    async def save_results(self):
        """Test sonuçlarını dosyaya kaydet"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_filename = f"face_recognition_test_results_{timestamp}.json"
        
        try:
            with open(results_filename, 'w', encoding='utf-8') as f:
                json.dump({
                    'test_info': {
                        'camera_id': self.camera_id,
                        'timestamp': datetime.now().isoformat(),
                        'total_images': len(self.results)
                    },
                    'results': self.results
                }, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Results saved to: {results_filename}")
        except Exception as e:
            logger.error(f"Error saving results: {e}")
    
    def print_summary(self):
        """Test sonuçlarının özetini yazdır"""
        logger.info(f"\n{'='*60}")
        logger.info("TEST SUMMARY")
        logger.info(f"{'='*60}")
        
        total_images = len(self.results)
        successful_tests = sum(1 for r in self.results if r['success'])
        total_faces = sum(r['faces_detected'] for r in self.results)
        recognized_faces = sum(
            len([f for f in r['faces'] if f['recognition_status'] == 'SUCCESS'])
            for r in self.results
        )
        unknown_faces = sum(
            len([f for f in r['faces'] if f['recognition_status'] == 'UNKNOWN'])
            for r in self.results
        )
        
        logger.info(f"Total Images Tested: {total_images}")
        logger.info(f"Successful Tests: {successful_tests}")
        logger.info(f"Failed Tests: {total_images - successful_tests}")
        logger.info(f"Total Faces Detected: {total_faces}")
        logger.info(f"Recognized Faces: {recognized_faces}")
        logger.info(f"Unknown Faces: {unknown_faces}")
        
        if total_faces > 0:
            recognition_rate = (recognized_faces / total_faces) * 100
            logger.info(f"Recognition Rate: {recognition_rate:.2f}%")
        
        # Her görsel için detaylı sonuç
        logger.info(f"\nDETAILED RESULTS:")
        logger.info(f"-" * 60)
        
        for result in self.results:
            logger.info(f"\n📸 {result['filename']}:")
            if result['success']:
                if result['faces_detected'] > 0:
                    for face in result['faces']:
                        confidence_str = f" (Confidence: {face['confidence']:.3f})" if face['confidence'] is not None else ""
                        status_emoji = "✅" if face['recognition_status'] == 'SUCCESS' else "❓"
                        logger.info(f"  {status_emoji} {face['name']}{confidence_str}")
                else:
                    logger.info("  😶 No faces detected")
            else:
                logger.info(f"  ❌ Error: {result['error']}")


async def main():
    """Ana test fonksiyonu"""
    try:
        # Test edilecek kamera ID'si
        camera_id = 3
        
        # Test sınıfını oluştur ve çalıştır
        tester = FaceRecognitionTester(camera_id)
        await tester.run_test_suite()
        
    except KeyboardInterrupt:
        logger.info("\nTest interrupted by user")
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        raise


if __name__ == "__main__":
    # Event loop'u çalıştır
    asyncio.run(main())